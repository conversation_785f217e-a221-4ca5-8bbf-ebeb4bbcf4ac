#!/usr/bin/env python3
"""
Main Application Controller
Coordinates between different modules and handles application flow
"""

import streamlit as st
import os
import time
from datetime import datetime
from typing import Optional

# Import custom modules
import sys
sys.path.append('.')
from src.services.spam_detector import SpamDetector
from src.app.streamlit_facebook import Facebook<PERSON><PERSON>
from src.app.streamlit_monitor import AutoMonitor
from config.app_config import config

# Import UI and page modules
from ui_components import NotificationManager, load_custom_css
from dashboard import DashboardRenderer
from pages.manual_check import ManualCheckPage
from pages.pending_spam import PendingSpamPage
from pages.test_detector import TestDetectorPage
from pages.settings import SettingsPage
from pages.logs import LogsPage


class StreamlitJudolRemover:
    """Main application controller that coordinates between different modules"""
    
    def __init__(self):
        self.initialize_session_state()
        self.load_environment()
        self.initialize_services()
        self.initialize_page_renderers()
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'spam_detector' not in st.session_state:
            st.session_state.spam_detector = None
        if 'facebook_api' not in st.session_state:
            st.session_state.facebook_api = None
        if 'auto_monitor' not in st.session_state:
            st.session_state.auto_monitor = None
        if 'monitor_running' not in st.session_state:
            st.session_state.monitor_running = False
        if 'posts_cache' not in st.session_state:
            st.session_state.posts_cache = {}
        if 'comments_cache' not in st.session_state:
            st.session_state.comments_cache = {}
        if 'statistics' not in st.session_state:
            st.session_state.statistics = {
                'comments_processed': 0,
                'spam_removed': 0,
                'spam_detected': 0,
                'last_check': None,
                'start_time': None
            }
        if 'auto_delete_enabled' not in st.session_state:
            st.session_state.auto_delete_enabled = os.getenv('AUTO_DELETE_SPAM', 'true').lower() == 'true'
        if 'notifications' not in st.session_state:
            st.session_state.notifications = []
        if 'monitor_logs' not in st.session_state:
            st.session_state.monitor_logs = []
        if 'pending_spam' not in st.session_state:
            st.session_state.pending_spam = []
        if 'current_page' not in st.session_state:
            st.session_state.current_page = "Dashboard"
        if 'previous_page' not in st.session_state:
            st.session_state.previous_page = None
    
    def load_environment(self):
        """Load environment variables"""
        try:
            from dotenv import load_dotenv
            # Load .env from project root (two levels up from src/app/)
            env_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
            load_dotenv(env_path)
        except ImportError:
            st.warning("python-dotenv not installed. Make sure environment variables are set.")

        self.page_id = os.getenv('PAGE_ID')
        self.page_access_token = os.getenv('PAGE_ACCESS_TOKEN')
        self.model_path = os.getenv('MODEL_PATH', './src/models')
        self.confidence_threshold = float(os.getenv('CONFIDENCE_THRESHOLD', '0.5'))
    
    def initialize_services(self):
        """Initialize spam detector and Facebook API"""
        try:
            # Initialize spam detector
            if st.session_state.spam_detector is None:
                with st.spinner("Loading spam detection model..."):
                    st.session_state.spam_detector = SpamDetector(self.model_path)

            # Initialize Facebook API
            if st.session_state.facebook_api is None and self.page_access_token:
                st.session_state.facebook_api = FacebookAPI(
                    self.page_id,
                    self.page_access_token
                )

        except Exception as e:
            NotificationManager.show_notification(f"Error initializing services: {str(e)}", "error", 8000)
    
    def initialize_page_renderers(self):
        """Initialize page renderer instances"""
        self.dashboard_renderer = DashboardRenderer(
            st.session_state.facebook_api,
            st.session_state.spam_detector,
            self.confidence_threshold
        )
        
        self.manual_check_page = ManualCheckPage(
            st.session_state.facebook_api,
            st.session_state.spam_detector,
            self.confidence_threshold
        )
        
        self.pending_spam_page = PendingSpamPage(st.session_state.facebook_api)
        
        self.test_detector_page = TestDetectorPage(
            st.session_state.spam_detector,
            self.confidence_threshold
        )
        
        self.settings_page = SettingsPage(
            self.page_id,
            self.page_access_token,
            self.model_path,
            self.confidence_threshold
        )
        
        self.logs_page = LogsPage()
    
    def render_sidebar(self):
        """Render sidebar with navigation and controls"""
        st.sidebar.markdown("## 🛡️ Judol Remover")
        st.sidebar.markdown("---")
        
        # Navigation
        page = st.sidebar.selectbox(
            "Navigate to:",
            ["Dashboard", "Manual Check", "Pending Spam", "Test Detector", "Settings", "Logs"]
        )
        
        st.sidebar.markdown("---")
        
        # Monitor controls
        st.sidebar.markdown("### 🔄 Auto Monitor")

        # Auto Delete Toggle
        auto_delete = st.sidebar.checkbox(
            "🗑️ Auto Delete Spam",
            value=st.session_state.auto_delete_enabled,
            help="Otomatis hapus komentar yang terdeteksi spam"
        )

        if auto_delete != st.session_state.auto_delete_enabled:
            st.session_state.auto_delete_enabled = auto_delete

            # Update auto monitor configuration if it exists
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                st.session_state.auto_monitor.update_config(auto_delete_enabled=auto_delete)

            NotificationManager.show_notification(f"Auto delete {'enabled' if auto_delete else 'disabled'}", "success", 2000)

        # Auto Refresh Toggle
        auto_refresh = st.sidebar.checkbox(
            "🔄 Auto Refresh UI",
            value=st.session_state.get('auto_refresh_enabled', False),
            help="Otomatis refresh dashboard setiap 10 detik (Disable untuk mencegah konflik komponen)"
        )
        st.session_state.auto_refresh_enabled = auto_refresh

        if st.session_state.monitor_running:
            if st.sidebar.button("⏹️ Stop Monitor", type="secondary"):
                self.stop_monitor()

            # Force sync statistics from auto monitor for real-time updates
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                try:
                    monitor_stats = st.session_state.auto_monitor.get_statistics()
                    if monitor_stats:
                        st.session_state.statistics.update(monitor_stats)
                except Exception:
                    pass

            # Show monitor status with more frequent runtime updates
            if st.session_state.statistics['start_time']:
                # Calculate runtime with precise seconds
                runtime = datetime.now() - st.session_state.statistics['start_time']
                total_seconds = int(runtime.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60

                if hours > 0:
                    runtime_str = f"{hours}h {minutes}m {seconds}s"
                else:
                    runtime_str = f"{minutes}m {seconds}s"

                # Add live indicator
                st.sidebar.metric("Runtime", f"🔴 {runtime_str}", help="Updates every second")

            # Real-time metrics with smooth transitions
            st.sidebar.metric("Comments Processed", st.session_state.statistics['comments_processed'])
            st.sidebar.metric("Spam Detected", st.session_state.statistics['spam_detected'])
            st.sidebar.metric("Spam Removed", st.session_state.statistics['spam_removed'])

            # Show last update time for transparency
            if st.session_state.statistics.get('last_check'):
                last_update = st.session_state.statistics['last_check']
                if isinstance(last_update, datetime):
                    time_diff = (datetime.now() - last_update).total_seconds()
                    if time_diff < 60:
                        st.sidebar.caption(f"🔄 Updated {int(time_diff)}s ago")
                    else:
                        st.sidebar.caption(f"🔄 Updated {int(time_diff//60)}m ago")
        else:
            if st.sidebar.button("▶️ Start Monitor", type="primary"):
                self.start_monitor()
        
        st.sidebar.markdown("---")
        
        # System status
        st.sidebar.markdown("### 📊 System Status")

        # Model status
        model_status = "🟢 Ready" if st.session_state.spam_detector else "🔴 Not Loaded"
        st.sidebar.markdown(f"**Model:** {model_status}")

        # Facebook API status
        fb_status = "🟢 Connected" if st.session_state.facebook_api else "🔴 Not Connected"
        st.sidebar.markdown(f"**Facebook:** {fb_status}")

        # Auto Monitor status
        if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
            monitor_status = "🟢 Initialized"
        else:
            monitor_status = "🔴 Not Initialized"
        st.sidebar.markdown(f"**Auto Monitor:** {monitor_status}")

        return page

    def start_monitor(self):
        """Start the auto monitor"""
        try:
            if not st.session_state.facebook_api:
                NotificationManager.show_notification("Facebook API not connected", "error", 5000)
                return

            if not st.session_state.spam_detector:
                NotificationManager.show_notification("Spam detector not loaded", "error", 5000)
                return

            # Initialize auto monitor
            st.session_state.auto_monitor = AutoMonitor(
                facebook_api=st.session_state.facebook_api,
                spam_detector=st.session_state.spam_detector,
                confidence_threshold=self.confidence_threshold,
                auto_delete_enabled=st.session_state.auto_delete_enabled
            )

            # Start monitoring
            st.session_state.auto_monitor.start()
            st.session_state.monitor_running = True
            st.session_state.statistics['start_time'] = datetime.now()

            NotificationManager.show_notification("Auto monitor started!", "success", 3000)

        except Exception as e:
            NotificationManager.show_notification(f"Failed to start monitor: {str(e)}", "error", 6000)

    def stop_monitor(self):
        """Stop the auto monitor"""
        try:
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                st.session_state.auto_monitor.stop()

            st.session_state.monitor_running = False
            st.session_state.statistics['start_time'] = None

            NotificationManager.show_notification("Auto monitor stopped!", "info", 3000)

        except Exception as e:
            NotificationManager.show_notification(f"Error stopping monitor: {str(e)}", "error", 5000)

    def handle_auto_refresh(self):
        """Handle auto refresh functionality"""
        if (st.session_state.monitor_running and
            st.session_state.get('auto_refresh_enabled', False)):

            # Auto refresh every 10 seconds
            time.sleep(10)
            st.rerun()

    def run(self):
        """Main application entry point"""
        # Load custom CSS
        load_custom_css()

        # Set page config
        st.set_page_config(
            page_title="Judol Remover",
            page_icon="🛡️",
            layout="wide",
            initial_sidebar_state="expanded"
        )

        # Render sidebar and get current page
        current_page = self.render_sidebar()

        # Update current page in session state
        if st.session_state.current_page != current_page:
            st.session_state.previous_page = st.session_state.current_page
            st.session_state.current_page = current_page

        # Display notifications
        NotificationManager.display_notifications()

        # Route to appropriate page
        if current_page == "Dashboard":
            self.dashboard_renderer.render_dashboard()
        elif current_page == "Manual Check":
            self.manual_check_page.render()
        elif current_page == "Pending Spam":
            self.pending_spam_page.render()
        elif current_page == "Test Detector":
            self.test_detector_page.render()
        elif current_page == "Settings":
            self.settings_page.render()
        elif current_page == "Logs":
            self.logs_page.render()

        # Handle auto refresh (only if enabled and monitor is running)
        if (st.session_state.monitor_running and
            st.session_state.get('auto_refresh_enabled', False)):
            # Use a placeholder for auto refresh
            placeholder = st.empty()
            with placeholder.container():
                st.caption(f"🔄 Auto refresh enabled - Page will refresh in 10 seconds")
                time.sleep(10)
                st.rerun()


def main():
    """Application entry point"""
    app = StreamlitJudolRemover()
    app.run()


if __name__ == "__main__":
    main()
