#!/usr/bin/env python3
"""
Logs Page Module
Handles activity logs display and management
"""

import streamlit as st
import pandas as pd
import time
from datetime import datetime
from typing import List, Dict
from src.app.ui_components import NotificationManager


class LogsPage:
    """<PERSON><PERSON> logs page rendering and functionality"""
    
    def __init__(self):
        pass
    
    def render(self):
        """Render logs page with real-time updates - LOGS PAGE ONLY"""
        # CRITICAL: This method should ONLY be called when current_page == "Logs"
        # If this appears in Dashboard, there's a routing bug

        # Real-time header with auto-refresh indicator
        col1, col2 = st.columns([3, 1])

        with col1:
            st.markdown("### 📋 Activity Logs")

        with col2:
            # Show auto-refresh status (controlled from sidebar)
            if (st.session_state.monitor_running and
                st.session_state.get('auto_refresh_enabled', True)):
                st.success("🔄 Auto Refresh ON")
            elif st.session_state.monitor_running:
                st.warning("⏸️ Auto Refresh OFF")
            else:
                st.info("⏹️ Monitor Stopped")

        # Initialize logs if not exists
        if 'monitor_logs' not in st.session_state:
            st.session_state.monitor_logs = []

        # Show auto-refresh instructions
        if not st.session_state.monitor_running:
            st.info("ℹ️ Start the monitor from the sidebar to see real-time logs")
        elif not st.session_state.get('auto_refresh_enabled', True):
            st.warning("⚠️ Auto-refresh is disabled. Enable 'Auto Refresh UI' in the sidebar for real-time updates")
        else:
            st.success("✅ Real-time logs enabled - Updates every 5 seconds automatically")

        # CRITICAL: Force sync logs from auto monitor on every page load
        if ('auto_monitor' in st.session_state and
            st.session_state.auto_monitor is not None and
            st.session_state.monitor_running):
            try:
                if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                    synced_count = st.session_state.auto_monitor.sync_logs_to_session_state()
                    # Show sync status in real-time
                    if synced_count > 0:
                        st.caption(f"🔄 Last sync: {datetime.now().strftime('%H:%M:%S')} - {synced_count} logs")
            except Exception:
                pass

        # Real-time metrics
        self._render_metrics()

        # Real-time log controls
        self._render_log_controls()

        # Display logs
        self._render_logs_display()

    def _render_metrics(self):
        """Render real-time metrics"""
        st.markdown("#### 📊 Real-time Metrics")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_logs = len(st.session_state.monitor_logs)
            st.metric("Total Logs", total_logs)

        with col2:
            monitor_status = "🟢 Running" if st.session_state.monitor_running else "🔴 Stopped"
            st.metric("Monitor Status", monitor_status)

        with col3:
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                processed_count = len(st.session_state.auto_monitor.processed_comments)
                internal_logs_count = len(st.session_state.auto_monitor.internal_logs)
                st.metric("Processed Comments", processed_count)
            else:
                internal_logs_count = 0
                st.metric("Processed Comments", "N/A")

        with col4:
            # Show sync status
            if internal_logs_count > 0:
                sync_status = "🟢 Synced" if internal_logs_count == total_logs else "🟡 Pending"
                st.metric("Sync Status", sync_status)
            else:
                st.metric("Sync Status", "N/A")

        # Show latest activity timestamp
        if st.session_state.monitor_logs:
            latest_log = st.session_state.monitor_logs[-1]
            latest_time = latest_log.get('timestamp', 'Unknown')
            st.info(f"🕒 Latest Activity: {latest_time}")
        else:
            st.info("🕒 No activity recorded yet")

    def _render_log_controls(self):
        """Render log control buttons"""
        st.markdown("#### 🎛️ Log Controls")
        col1, col2, col3, col4 = st.columns([1, 1, 1, 1])

        with col1:
            if st.button("🔄 Manual Refresh"):
                # Force sync from auto monitor
                if ('auto_monitor' in st.session_state and
                    st.session_state.auto_monitor is not None):
                    try:
                        if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                            synced_count = st.session_state.auto_monitor.sync_logs_to_session_state()
                            NotificationManager.show_notification(f"Refreshed! Synced {synced_count} logs", "success", 2000)
                    except Exception as e:
                        NotificationManager.show_notification(f"Error: {str(e)}", "error", 3000)
                st.rerun()

        with col2:
            if st.button("🗑️ Clear Logs"):
                st.session_state.monitor_logs = []
                # Also clear internal logs if available
                if ('auto_monitor' in st.session_state and
                    st.session_state.auto_monitor is not None):
                    try:
                        st.session_state.auto_monitor.internal_logs = []
                    except Exception:
                        pass
                NotificationManager.show_notification("All logs cleared!", "info", 2000)
                st.rerun()

        with col3:
            if st.button("🔄 Force Sync"):
                if ('auto_monitor' in st.session_state and
                    st.session_state.auto_monitor is not None):
                    try:
                        if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                            synced_count = st.session_state.auto_monitor.sync_logs_to_session_state()
                            NotificationManager.show_notification(f"Force synced {synced_count} logs", "success", 2000)
                        else:
                            NotificationManager.show_notification("Sync method not available", "warning", 2000)
                    except Exception as e:
                        NotificationManager.show_notification(f"Sync error: {str(e)}", "error", 3000)
                else:
                    NotificationManager.show_notification("Auto monitor not available", "warning", 2000)
                st.rerun()

        with col4:
            if st.button("🧪 Add Test Log"):
                test_log = {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'TEST',
                    'comment_id': f'test_{int(time.time())}',
                    'author': 'Test User',
                    'message': f'Test log entry at {datetime.now().strftime("%H:%M:%S")}',
                    'post_id': 'test_post',
                    'reason': 'Manual test'
                }
                st.session_state.monitor_logs.append(test_log)
                NotificationManager.show_notification("Test log added!", "success", 2000)
                st.rerun()

    def _render_logs_display(self):
        """Render the main logs display"""
        logs = st.session_state.monitor_logs

        # Force refresh logs from auto monitor
        if st.button("🔄 Force Sync Logs from Monitor"):
            if 'auto_monitor' in st.session_state and st.session_state.auto_monitor is not None:
                try:
                    # Check if method exists (for backward compatibility)
                    if hasattr(st.session_state.auto_monitor, 'sync_logs_to_session_state'):
                        total_logs = st.session_state.auto_monitor.sync_logs_to_session_state()
                        st.success(f"Synced logs from auto monitor. Total logs: {total_logs}")
                    else:
                        # Fallback: get recent activity and replace session logs
                        recent_activity = st.session_state.auto_monitor.get_recent_activity(50)
                        if recent_activity:
                            st.session_state.monitor_logs = recent_activity
                            st.success(f"Synced {len(recent_activity)} logs from auto monitor (fallback method)")
                        else:
                            st.warning("No logs found in auto monitor")
                except Exception as e:
                    st.error(f"Error syncing logs: {e}")
                    # Show restart option
                    if st.button("🔄 Reset Auto Monitor"):
                        st.session_state.auto_monitor = None
                        st.success("Auto monitor reset. Please restart it from the sidebar.")
                        st.rerun()
            else:
                st.warning("Auto monitor not available")
            st.rerun()

        if not logs:
            st.info("📭 No activity logs yet. Start the auto monitor to see activity.")

            # Troubleshooting tips
            st.markdown("**Troubleshooting:**")
            st.write("1. Check if auto monitor is running")
            st.write("2. Try 'Force Sync Logs from Monitor' button above")
            st.write("3. Use 'Add Test Log' button below to add sample data")

            # Add test log button for empty state
            col1, col2 = st.columns([1, 3])
            with col1:
                if st.button("🧪 Add Test Log", key="empty_state_test_log"):
                    test_log = {
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'action': 'TEST',
                        'comment_id': f'test_{int(time.time())}',
                        'author': 'Test User',
                        'message': f'Test log entry at {datetime.now().strftime("%H:%M:%S")}',
                        'post_id': 'test_post',
                        'reason': 'Manual test'
                    }
                    st.session_state.monitor_logs.append(test_log)
                    NotificationManager.show_notification("Test log added!", "success", 2000)
                    st.rerun()
            return

        st.markdown(f"#### Recent Activity ({len(logs)} entries)")

        # Filter options
        col1, col2 = st.columns(2)

        with col1:
            log_filter = st.selectbox(
                "Filter by action:",
                ["All", "NEW_COMMENT", "SPAM_DETECTED", "PENDING_SPAM", "DELETED", "TEST", "ERROR", "INFO"]
            )

        with col2:
            show_count = st.number_input(
                "Show last N entries:",
                min_value=10,
                max_value=100,
                value=50,
                step=10
            )

        # Debug info
        st.write(f"**Debug Info:** Total logs: {len(logs)}, Filter: {log_filter}")
        if logs:
            st.write(f"**Sample log actions:** {[log.get('action', 'NO_ACTION') for log in logs[:3]]}")

        # Filter and display logs
        filtered_logs = logs
        if log_filter != "All":
            filtered_logs = [log for log in logs if log.get('action') == log_filter]

        st.write(f"**After filter:** {len(filtered_logs)} logs")

        # Show most recent entries
        recent_logs = filtered_logs[-show_count:]
        recent_logs.reverse()  # Show newest first

        st.write(f"**Recent logs to display:** {len(recent_logs)} logs")

        # Real-time log display - ALWAYS show tabs even if empty
        if recent_logs:
            self._render_logs_tabs(recent_logs)
        else:
            st.info(f"No logs found for filter: {log_filter}")
            # Still show empty tabs for debugging
            st.markdown("**Showing empty table for debugging:**")
            self._render_logs_tabs([])

            # Show helpful message
            if st.session_state.monitor_running:
                st.markdown("""
                **💡 Tips for real-time logs:**
                - Enable "🔄 Auto Refresh" checkbox above for automatic updates
                - Use "🔄 Manual Refresh" button to force immediate update
                - Use "🔄 Force Sync" to sync from internal monitor storage
                """)
            else:
                st.warning("🔴 Auto monitor is not running. Start it from the sidebar to see real-time logs.")

    def _render_logs_tabs(self, recent_logs: List[Dict]):
        """Render logs in both table and card format"""
        # Show logs in both table and card format
        tab1, tab2 = st.tabs(["📊 Table View", "📋 Card View"])

        with tab1:
            self._render_table_view(recent_logs)

        with tab2:
            self._render_card_view(recent_logs)

    def _render_table_view(self, recent_logs: List[Dict]):
        """Render logs in table format"""
        # Table format for quick overview
        log_data = []

        # Handle empty logs case
        if not recent_logs:
            st.info("📊 No logs to display in table format")
            # Show empty table structure
            empty_df = pd.DataFrame({
                'Time': [],
                'Action': [],
                'Author': [],
                'Message': [],
                'Reason': []
            })
            st.dataframe(empty_df, use_container_width=True, height=200)
            return

        for log in recent_logs:
            # Add emoji based on action
            action = log.get('action', '')
            if action == 'NEW_COMMENT':
                action_display = "💬 NEW_COMMENT"
            elif action == 'SPAM_DETECTED':
                action_display = "🚨 SPAM_DETECTED"
            elif action == 'DELETED':
                action_display = "🗑️ DELETED"
            elif action == 'PENDING_SPAM':
                action_display = "⏳ PENDING_SPAM"
            elif action == 'TEST':
                action_display = "🧪 TEST"
            else:
                action_display = f"ℹ️ {action}"

            log_data.append({
                'Time': log.get('timestamp', '')[-8:] if log.get('timestamp') else '',  # Show only time
                'Action': action_display,
                'Author': log.get('author', '')[:20] + "..." if len(log.get('author', '')) > 20 else log.get('author', ''),
                'Message': log.get('message', '')[:40] + "..." if len(log.get('message', '')) > 40 else log.get('message', ''),
                'Reason': log.get('reason', '')[:30] + "..." if len(log.get('reason', '')) > 30 else log.get('reason', '')
            })

        df = pd.DataFrame(log_data)
        st.dataframe(df, use_container_width=True, height=400)

    def _render_card_view(self, recent_logs: List[Dict]):
        """Render logs in card format"""
        # Card format for detailed view
        st.markdown("#### 📝 Real-time Activity Stream")

        # Show latest 20 logs in card format
        for i, log in enumerate(recent_logs[:20]):
            timestamp = log.get('timestamp', 'Unknown time')
            action = log.get('action', 'UNKNOWN')
            author = log.get('author', 'Unknown')
            message = log.get('message', '')
            reason = log.get('reason', '')

            # Choose color and emoji based on action
            if action == 'NEW_COMMENT':
                emoji = "💬"
                color = "#e3f2fd"
                border_color = "#2196f3"
            elif action == 'SPAM_DETECTED':
                emoji = "🚨"
                color = "#fff3e0"
                border_color = "#ff9800"
            elif action == 'DELETED':
                emoji = "🗑️"
                color = "#ffebee"
                border_color = "#f44336"
            elif action == 'PENDING_SPAM':
                emoji = "⏳"
                color = "#fff8e1"
                border_color = "#ffc107"
            elif action == 'TEST':
                emoji = "🧪"
                color = "#f3e5f5"
                border_color = "#9c27b0"
            else:
                emoji = "ℹ️"
                color = "#f5f5f5"
                border_color = "#9e9e9e"

            # Create activity card with real-time styling
            st.markdown(f"""
            <div style="
                background-color: {color};
                padding: 12px;
                border-radius: 8px;
                margin: 8px 0;
                border-left: 4px solid {border_color};
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: all 0.3s ease;
            ">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
                    <strong style="font-size: 16px;">{emoji} {action}</strong>
                    <small style="color: #666; font-size: 12px;">{timestamp}</small>
                </div>
                <div style="margin: 4px 0;">
                    <strong>Author:</strong> {author}
                </div>
                <div style="margin: 4px 0;">
                    <strong>Message:</strong> {message[:100]}{'...' if len(message) > 100 else ''}
                </div>
                <div style="margin: 4px 0;">
                    <small style="color: #666;"><em>{reason}</em></small>
                </div>
            </div>
            """, unsafe_allow_html=True)

            # Add separator for readability
            if i < len(recent_logs[:20]) - 1:
                st.markdown("<hr style='margin: 5px 0; border: 0; border-top: 1px solid #eee;'>", unsafe_allow_html=True)
